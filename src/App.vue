<template>
  <section class="todoapp">
    <header class="header">
      <h1>待办事项</h1>
      <input type="text" class="new-todo" placeholder="请输入待办事项" @keyup.enter="addTodo" />
    </header>
    <section class="main">
      <input type="checkbox" id="toggle-all" class="toggle-all" v-model="allDone" />
      <label for="toggle-all">全部完成</label>
      <ul class="todo-list">
        <li
          v-for="todo in filteredTodos"
          :key="todo.id"
          class="todo"
          :class="{ completed: todo.completed }"
        >
          <div class="view">
            <input type="checkbox" class="toggle" v-model="todo.completed" />
            <label @dblclick="editTodo(todo)">{{ todo.text }}</label>
            <input
              type="text"
              class="edit"
              v-model="todo.text"
              v-if="editedTodo === todo"
              @blur="doneEdit(todo)"
              @keyup.enter="doneEdit(todo)"
              @keyup.esc="cancelEdit(todo)"
            />
            <button class="destroy" @click="removeTodo(todo.id)"></button>
          </div>
        </li>
      </ul>
    </section>
    <footer class="footer">
      <span class="todo-count">
        剩余
        <strong>{{ filteredTodos.length }}</strong>
        项待办事项
      </span>
      <ul class="filters">
        <li>
          <a
            href="javascript:void(0)"
            :class="{ selected: visibility === 'all' }"
            @click="visibility = 'all'"
            >全部</a
          >
        </li>
        <li>
          <a
            href="javascript:void(0)"
            :class="{ selected: visibility === 'active' }"
            @click="visibility = 'active'"
            >未完成</a
          >
        </li>
        <li>
          <a
            href="javascript:void(0)"
            :class="{ selected: visibility === 'completed' }"
            @click="visibility = 'completed'"
            >已完成</a
          >
        </li>
      </ul>
    </footer>
  </section>
</template>

<script setup>
//每一个数据的形式是：{id: number, text: string, completed: boolean}
import { ref, computed, watchEffect } from 'vue'
const STORAGE_KEY = 'todo-list'
const todos = ref(JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]'))
const visibility = ref('all')
const filters = {
  all: (todos) => todos,
  active: (todos) => todos.filter((todo) => !todo.completed),
  completed: (todos) => todos.filter((todo) => todo.completed),
}
const filteredTodos = computed(() => filters[visibility.value](todos.value))
const editedTodo = ref(null)
const removeTodo = (id) => {
  todos.value = todos.value.filter((todo) => todo.id !== id)
}
const addTodo = (e) => {
  const text = e.target.value
  if (text.trim()) {
    todos.value.push({
      id: todos.value.length + 1,
      text,
      completed: false,
    })
    e.target.value = ''
  }
}
const allDone = computed({
  get() {
    return filteredTodos.value.every((todo) => todo.completed)
  },
  set(value) {
    filteredTodos.value.forEach((todo) => {
      todo.completed = value
    })
  },
})

watchEffect(() => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(todos.value))
})
</script>

<style lang="scss" scoped>
@import './assets/todo.css';
</style>
